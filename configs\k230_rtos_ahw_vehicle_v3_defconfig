CONFIG_BOARD_CONFIG_NAME="k230_rtos_ahw_vehicle_v3_defconfig"
CONFIG_BOARD_K230_AHW_VEHICLE_V3=y
CONFIG_BOARD_NAME="K230_aihardware"
CONFIG_AUTO_DETECT_DDR_SIZE=y
# CONFIG_AUTO_DDR_SIZE_2048 is not set
# CONFIG_RT_AUTO_RESIZE_PARTITION is not set
CONFIG_RTT_AUTO_EXEC_CMD="/sdcard/app/examples/integrated_poc/triple_ahd_demo_muyi.elf -K \"/sdcard/app/examples/integrated_poc\" &"
# CONFIG_ENABLE_NETWORK_RT_WLAN is not set
CONFIG_RTT_ENABLE_BUILD_EXAMPLES=y
CONFIG_RTT_ENABLE_BUILD_INTEGRATED_EXAMPLES=y
CONFIG_MPP_ENABLE_CSI_DEV_0=y
CONFIG_MPP_CSI_DEV0_RESET=27
CONFIG_MPP_ENABLE_CSI_DEV_1=y
CONFIG_MPP_CSI_DEV1_RESET=29
CONFIG_MPP_ENABLE_CSI_DEV_2=y
CONFIG_MPP_CSI_DEV2_RESET=31
CONFIG_MPP_CSI_DEV2_I2C_DEV="i2c3"
CONFIG_MPP_CSI_DEV2_MCLK_2=y
CONFIG_MPP_ENABLE_SENSOR_OV5647=y
CONFIG_MPP_SENSOR_OV5647_ON_CSI2_USE_CHIP_CLK=y
CONFIG_MPP_ENABLE_SENSOR_XS9950=y
CONFIG_MPP_ENABLE_DSI_LCD=y
CONFIG_MPP_DSI_LCD_RESET_PIN=13
CONFIG_MPP_DSI_LCD_BACKLIGHT_PIN=61
CONFIG_MPP_DSI_ENABLE_LCD_ST7701=y
CONFIG_MPP_ENABLE_MIDDLEWARE_LIB_RTSP_PUSHER=y
